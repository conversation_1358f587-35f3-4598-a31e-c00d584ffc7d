export type SiteConfig = typeof siteConfig;

export const siteConfig = {
  name: "Next.js + HeroUI",
  description: "Make beautiful websites regardless of your design experience.",
  tabItems: [
    {
      key: "home",
      label: "首页",
      icon: "HomeIcon",
    },
    {
      key: "host",
      label: "主持",
      icon: "HostIcon",
    }
  ],
  checkboxOptions: {
    hostControls: [
      { value: "previous", label: "上一题", icon: "PreviousIcon" },
      { value: "next", label: "下一题", icon: "NextIcon" },
      { value: "showAnswer", label: "公布答案", icon: "ShowAnswerIcon" },
      { value: "playerAnswer", label: "选手答案", icon: "PlayerAnswerIcon" },
      { value: "mainScreen", label: "主画面", icon: "MainScreenIcon" },
      { value: "vote", label: "开启投票", icon: "VoteIcon" },
      { value: "timer", label: "切换计时", icon: "PkIcon" },
      { value: "mute", label: "静音", icon: "MuteIcon" },
    ],
  },
  links: {
    github: "https://github.com/heroui-inc/heroui",
    twitter: "https://twitter.com/hero_ui",
    docs: "https://heroui.com",
    discord: "https://discord.gg/9b6yyZKmH4",
    sponsor: "https://patreon.com/jrgarciadev",
  },
};
